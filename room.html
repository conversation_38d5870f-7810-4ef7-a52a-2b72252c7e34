<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Benighter Connect - Video Room</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🎥</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1f2937;
            color: #ffffff;
            overflow: hidden;
        }

        .header {
            background: #374151;
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #4b5563;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .room-info {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo-small {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: #ffffff;
        }

        .logo-icon-small {
            width: 28px;
            height: 28px;
            background: #2563eb;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .brand-name {
            color: #2563eb;
        }

        .room-id {
            background: #4b5563;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
            cursor: pointer;
            border: 1px solid #6b7280;
            transition: all 0.2s ease;
            color: #e5e7eb;
            font-size: 0.875rem;
        }

        .room-id:hover {
            background: #6b7280;
            border-color: #9ca3af;
        }

        .controls {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.375rem 0.875rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn.danger {
            background: #dc2626;
        }

        .btn.danger:hover {
            background: #b91c1c;
        }

        .participants {
            background: #4b5563;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            color: #e5e7eb;
            border: 1px solid #6b7280;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 70px);
        }

        .video-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1f2937;
        }

        .videos-grid {
            flex: 1;
            display: grid;
            gap: 8px;
            padding: 8px;
            height: calc(100vh - 128px);
            overflow-x: auto;
            overflow-y: hidden;
            /* Dynamic grid properties will be set by JavaScript */
        }

        .video-container {
            position: relative;
            background: #111827;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 1px solid #374151;
            aspect-ratio: 16/9;
            /* Dynamic sizing will be set by JavaScript */
        }

        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-label {
            position: absolute;
            bottom: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.8);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            color: white;
            font-weight: 500;
        }

        .chat-panel {
            width: 320px;
            background: #374151;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #4b5563;
        }

        .participants-panel {
            width: 280px;
            background: #374151;
            display: none;
            flex-direction: column;
            border-left: 1px solid #4b5563;
        }

        .panel-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #4b5563;
            font-weight: 600;
            color: #ffffff;
            background: #4b5563;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close-panel {
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-panel:hover {
            color: #ffffff;
        }

        .participants-list {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .participant-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #4b5563;
            border-radius: 8px;
            border: 1px solid #6b7280;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .participant-item:hover {
            background: #6b7280;
        }

        .participant-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 0.75rem;
            font-size: 0.875rem;
        }

        .participant-name {
            color: #ffffff;
            font-weight: 500;
        }

        .participant-status {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        .chat-btn {
            background: #3b82f6;
            border: none;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .chat-btn:hover {
            background: #2563eb;
        }

        .chat-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #4b5563;
            font-weight: 600;
            color: #ffffff;
            background: #4b5563;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #374151;
        }

        .message {
            margin-bottom: 0.75rem;
            padding: 0.5rem 0.75rem;
            background: #4b5563;
            border-radius: 8px;
            border: 1px solid #6b7280;
        }

        .message.private {
            background: #1e40af;
            border: 1px solid #3b82f6;
        }

        .message.private-sent {
            background: #059669;
            border: 1px solid #10b981;
        }

        .private-indicator {
            font-size: 0.75rem;
            color: #fbbf24;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .message-sender {
            font-weight: 600;
            color: #60a5fa;
            font-size: 0.75rem;
        }

        .message-time {
            font-size: 0.625rem;
            color: #9ca3af;
            float: right;
        }

        .chat-input {
            padding: 1rem 1.5rem;
            border-top: 1px solid #4b5563;
            background: #4b5563;
        }

        .chat-input input {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #6b7280;
            border-radius: 6px;
            background: #374151;
            color: #ffffff;
            font-family: inherit;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .chat-input input:focus {
            outline: none;
            border-color: #2563eb;
            background: #4b5563;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .bottom-controls {
            background: #374151;
            padding: 1rem;
            display: flex;
            justify-content: center;
            gap: 0.75rem;
            border-top: 1px solid #4b5563;
        }

        .control-btn {
            background: #6b7280;
            color: #ffffff;
            border: none;
            padding: 0;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.125rem;
            width: 44px;
            height: 44px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: #9ca3af;
            transform: scale(1.05);
        }

        .control-btn.active {
            background: #2563eb;
            color: white;
        }

        .control-btn.active:hover {
            background: #1d4ed8;
        }

        .control-btn.danger {
            background: #dc2626;
            color: white;
        }

        .control-btn.danger:hover {
            background: #b91c1c;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .room-info {
                flex-direction: column;
                gap: 0.75rem;
                align-items: center;
            }

            .logo-small {
                font-size: 1.1rem;
            }

            .main-content {
                flex-direction: column;
            }

            .chat-panel {
                width: 100%;
                height: 300px;
                order: -1;
            }

            .videos-grid {
                grid-template-columns: 1fr !important;
                grid-template-rows: auto !important;
                padding: 8px;
                gap: 8px;
                overflow-y: auto !important;
                overflow-x: hidden !important;
            }

            .video-container {
                min-height: 180px;
                aspect-ratio: 16/9;
            }

            .bottom-controls {
                padding: 0.75rem;
                gap: 0.75rem;
            }

            .control-btn {
                width: 50px;
                height: 50px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="room-info">
            <div class="logo-small">
                <div class="logo-icon-small">🎥</div>
                <span><span class="brand-name">Benighter</span> Connect</span>
            </div>
            <div class="room-id" onclick="copyRoomId()" title="Click to copy room ID">
                Room: <span id="roomIdDisplay"></span>
            </div>
            <div class="participants">
                <span id="participantCount">1</span> participant(s)
            </div>
        </div>
        <div class="controls">
            <button class="btn" onclick="copyRoomLink()">📋 Copy Link</button>
            <button class="btn danger" onclick="leaveRoom()">🚪 Leave</button>
        </div>
    </div>

    <div class="main-content">
        <div class="video-area">
            <div class="videos-grid" id="videosGrid">
                <div class="video-container">
                    <video id="localVideo" autoplay muted></video>
                    <div class="video-label" id="localVideoLabel"></div>
                </div>
            </div>

            <div class="bottom-controls">
                <button class="control-btn active" id="videoBtn" onclick="toggleVideo()">📹</button>
                <button class="control-btn active" id="audioBtn" onclick="toggleAudio()">🎤</button>
                <button class="control-btn" id="screenBtn" onclick="toggleScreenShare()">🖥️</button>
                <button class="control-btn" id="participantsBtn" onclick="toggleParticipants()">👥</button>
                <button class="control-btn" id="chatBtn" onclick="toggleChat()">💬</button>
            </div>
        </div>

        <div class="participants-panel" id="participantsPanel">
            <div class="panel-header">
                👥 Participants
                <button class="close-panel" onclick="toggleParticipants()">×</button>
            </div>
            <div class="participants-list" id="participantsList"></div>
        </div>

        <div class="chat-panel" id="chatPanel">
            <div class="panel-header">
                💬 Chat
                <button class="close-panel" onclick="toggleChat()">×</button>
            </div>
            <div class="chat-messages" id="chatMessages"></div>
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type a message..." onkeypress="handleChatKeypress(event)">
            </div>
        </div>
    </div>

    <script>
        // Configuration for Benighter Connect
        const CONFIG = {
          // Backend server URL - Update this after deploying to Render
          BACKEND_URL: 'https://benighter-connect-backend.onrender.com', // Render deployment URL

          // Development URL (for local testing)
          DEV_BACKEND_URL: 'http://localhost:3002',

          // Determine which URL to use
          getBackendUrl: function() {
            // Use production URL if we're not on localhost
            if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
              return this.BACKEND_URL;
            }
            return this.DEV_BACKEND_URL;
          }
        };

        // Make it globally available
        window.CONFIG = CONFIG;
    </script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        // Get room ID and user name from URL
        const roomId = window.location.pathname.split('/')[2];
        const urlParams = new URLSearchParams(window.location.search);
        const userName = urlParams.get('name') || 'You';
        const roomPassword = urlParams.get('password') || '';

        // Display room ID
        document.getElementById('roomIdDisplay').textContent = roomId;

        // Initialize Socket.IO
        const socket = io(CONFIG.getBackendUrl());

        // WebRTC configuration with multiple STUN servers
        const configuration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' },
                { urls: 'stun:stun3.l.google.com:19302' },
                { urls: 'stun:stun4.l.google.com:19302' }
            ],
            iceCandidatePoolSize: 10
        };

        // Local stream and peer connections
        let localStream;
        let peerConnections = {};
        let isVideoEnabled = true;
        let isAudioEnabled = true;
        let isScreenSharing = false;

        // Participants and chat state
        let participants = [];
        let currentPrivateChat = null;
        let lastMessage = '';
        let lastMessageTime = 0;

        // Initialize the app
        async function init() {
            try {
                // Get user media
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });

                // Display local video
                document.getElementById('localVideo').srcObject = localStream;

                // Update local video label with user name
                document.getElementById('localVideoLabel').textContent = userName;

                // Apply preview settings if available
                const previewVideoEnabled = sessionStorage.getItem('previewVideoEnabled');
                const previewAudioEnabled = sessionStorage.getItem('previewAudioEnabled');

                if (previewVideoEnabled !== null) {
                    isVideoEnabled = previewVideoEnabled === 'true';
                    localStream.getVideoTracks().forEach(track => track.enabled = isVideoEnabled);
                    updateVideoButton();
                }

                if (previewAudioEnabled !== null) {
                    isAudioEnabled = previewAudioEnabled === 'true';
                    localStream.getAudioTracks().forEach(track => track.enabled = isAudioEnabled);
                    updateAudioButton();
                }

                // Clear preview settings
                sessionStorage.removeItem('previewVideoEnabled');
                sessionStorage.removeItem('previewAudioEnabled');

                // Set up initial grid layout
                updateGridLayout();

                // Join the room
                socket.emit('join-room', roomId, userName, roomPassword);

                console.log('✅ Successfully initialized video chat');
            } catch (error) {
                console.error('❌ Error accessing media devices:', error);
                alert('Could not access camera/microphone. Please check permissions.');
            }
        }

        // Socket event handlers
        socket.on('user-joined', async (user) => {
            console.log('👤 User joined:', user.name, 'ID:', user.id);
            console.log('🔗 Creating peer connection for user:', user.id);
            await createPeerConnection(user.id);
            updateParticipantCount();
        });

        socket.on('user-left', (userId) => {
            console.log('👋 User left:', userId);
            if (peerConnections[userId]) {
                peerConnections[userId].close();
                delete peerConnections[userId];
            }
            removeVideoElement(userId);
            updateParticipantCount();
        });

        // Handle current users when joining a room
        socket.on('current-users', async (userIds) => {
            console.log('👥 Current users in room:', userIds);
            // Create peer connections with all existing users
            for (const userId of userIds) {
                await createPeerConnection(userId);
            }
            updateParticipantCount();
        });

        socket.on('room-participants', async (participantList) => {
            console.log('📋 Received room participants:', participantList);
            participants = participantList;

            // Create peer connections with existing users (excluding self)
            for (const participant of participantList) {
                if (participant.id !== socket.id && !peerConnections[participant.id]) {
                    console.log('🔗 Creating peer connection with existing user:', participant.name, 'ID:', participant.id);
                    await createPeerConnection(participant.id);
                }
            }

            updateParticipantCount(participantList.length);
            updateParticipantsList();
        });

        socket.on('participants-updated', (participantList) => {
            participants = participantList;
            updateParticipantCount(participantList.length);
            updateParticipantsList();
        });

        // WebRTC signaling
        socket.on('offer', async (data) => {
            console.log('📥 Received offer from:', data.sender);
            await handleOffer(data.offer, data.sender);
        });

        socket.on('answer', async (data) => {
            console.log('📥 Received answer from:', data.sender);
            await handleAnswer(data.answer, data.sender);
        });

        socket.on('ice-candidate', async (data) => {
            console.log('🧊 Received ICE candidate from:', data.sender);
            await handleIceCandidate(data.candidate, data.sender);
        });

        // Chat functionality
        socket.on('chat-message', (data) => {
            addChatMessage(data.sender, data.message, data.timestamp);
        });

        socket.on('private-message', (data) => {
            addPrivateChatMessage(data.sender, data.message, data.timestamp, data.isSent, data.senderId, data.targetId);
        });

        socket.on('join-error', (errorMessage) => {
            alert(`Failed to join room: ${errorMessage}`);
            // Redirect back to home page
            window.location.href = '/';
        });

        // Create peer connection
        async function createPeerConnection(userId) {
            console.log('🔄 Creating peer connection for user:', userId);
            const peerConnection = new RTCPeerConnection(configuration);
            peerConnections[userId] = peerConnection;

            // Add local stream to peer connection
            localStream.getTracks().forEach(track => {
                console.log('➕ Adding track to peer connection:', track.kind);
                peerConnection.addTrack(track, localStream);
            });

            // Handle remote stream
            peerConnection.ontrack = (event) => {
                console.log('📺 Received remote stream from user:', userId);
                const remoteStream = event.streams[0];
                addVideoElement(userId, remoteStream);
            };

            // Handle ICE candidates
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    console.log('🧊 Sending ICE candidate to:', userId);
                    socket.emit('ice-candidate', {
                        candidate: event.candidate,
                        target: userId
                    });
                } else {
                    console.log('🧊 ICE gathering completed for:', userId);
                }
            };

            // Handle connection state changes
            peerConnection.onconnectionstatechange = () => {
                console.log(`🔗 Connection state with ${userId}:`, peerConnection.connectionState);
                if (peerConnection.connectionState === 'failed') {
                    console.error('❌ Connection failed with:', userId);
                    // Attempt to restart ICE
                    peerConnection.restartIce();
                }
            };

            // Handle ICE connection state changes
            peerConnection.oniceconnectionstatechange = () => {
                console.log(`🧊 ICE connection state with ${userId}:`, peerConnection.iceConnectionState);
                if (peerConnection.iceConnectionState === 'failed') {
                    console.error('❌ ICE connection failed with:', userId);
                }
            };

            // Create and send offer
            console.log('📤 Creating offer for user:', userId);
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);

            socket.emit('offer', {
                offer: offer,
                target: userId
            });
            console.log('✅ Offer sent to user:', userId);
        }

        // Handle incoming offer
        async function handleOffer(offer, senderId) {
            console.log('🔧 Handling offer from:', senderId);
            if (!peerConnections[senderId]) {
                console.log('🔄 Creating new peer connection for offer from:', senderId);
                const peerConnection = new RTCPeerConnection(configuration);
                peerConnections[senderId] = peerConnection;

                // Add local stream
                localStream.getTracks().forEach(track => {
                    console.log('➕ Adding track to peer connection for offer:', track.kind);
                    peerConnection.addTrack(track, localStream);
                });

                // Handle remote stream
                peerConnection.ontrack = (event) => {
                    console.log('📺 Received remote stream in offer handler from:', senderId);
                    const remoteStream = event.streams[0];
                    addVideoElement(senderId, remoteStream);
                };

                // Handle ICE candidates
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        console.log('🧊 Sending ICE candidate in offer handler to:', senderId);
                        socket.emit('ice-candidate', {
                            candidate: event.candidate,
                            target: senderId
                        });
                    } else {
                        console.log('🧊 ICE gathering completed in offer handler for:', senderId);
                    }
                };

                // Handle connection state changes
                peerConnection.onconnectionstatechange = () => {
                    console.log(`🔗 Connection state in offer handler with ${senderId}:`, peerConnection.connectionState);
                    if (peerConnection.connectionState === 'failed') {
                        console.error('❌ Connection failed in offer handler with:', senderId);
                        // Attempt to restart ICE
                        peerConnection.restartIce();
                    }
                };

                // Handle ICE connection state changes
                peerConnection.oniceconnectionstatechange = () => {
                    console.log(`🧊 ICE connection state in offer handler with ${senderId}:`, peerConnection.iceConnectionState);
                    if (peerConnection.iceConnectionState === 'failed') {
                        console.error('❌ ICE connection failed in offer handler with:', senderId);
                    }
                };
            }

            console.log('🔧 Setting remote description and creating answer for:', senderId);
            await peerConnections[senderId].setRemoteDescription(offer);
            const answer = await peerConnections[senderId].createAnswer();
            await peerConnections[senderId].setLocalDescription(answer);

            socket.emit('answer', {
                answer: answer,
                target: senderId
            });
            console.log('📤 Answer sent to:', senderId);
        }

        // Handle incoming answer
        async function handleAnswer(answer, senderId) {
            console.log('🔧 Handling answer from:', senderId);
            if (peerConnections[senderId]) {
                console.log('✅ Setting remote description for answer from:', senderId);
                await peerConnections[senderId].setRemoteDescription(answer);
            } else {
                console.error('❌ No peer connection found for answer from:', senderId);
            }
        }

        // Handle ICE candidate
        async function handleIceCandidate(candidate, senderId) {
            if (peerConnections[senderId]) {
                try {
                    console.log('🧊 Adding ICE candidate from:', senderId);
                    await peerConnections[senderId].addIceCandidate(candidate);
                    console.log('✅ ICE candidate added successfully from:', senderId);
                } catch (error) {
                    console.error('❌ Error adding ICE candidate from:', senderId, error);
                }
            } else {
                console.warn('⚠️ No peer connection found for ICE candidate from:', senderId);
            }
        }

        // Update grid layout based on participant count
        function updateGridLayout() {
            const videosGrid = document.getElementById('videosGrid');
            const videoContainers = videosGrid.querySelectorAll('.video-container');
            const participantCount = videoContainers.length;

            if (participantCount === 0) return;

            // Reset any previous styles
            videosGrid.style.gridAutoFlow = '';
            videosGrid.style.gridAutoColumns = '';
            videosGrid.style.overflowX = '';

            if (participantCount > 25) {
                // For more than 25 participants, use horizontal scrolling
                videosGrid.style.gridTemplateColumns = `repeat(${participantCount}, 250px)`;
                videosGrid.style.gridTemplateRows = '1fr';
                videosGrid.style.overflowX = 'auto';
                videosGrid.style.overflowY = 'hidden';
                videosGrid.style.gap = '8px';
            } else {
                // Calculate optimal grid layout for up to 25 participants
                let cols, rows, gap;

                if (participantCount === 1) {
                    cols = 1; rows = 1; gap = '16px';
                } else if (participantCount <= 2) {
                    cols = 2; rows = 1; gap = '24px';
                } else if (participantCount <= 4) {
                    cols = 2; rows = 2; gap = '20px';
                } else if (participantCount <= 6) {
                    cols = 3; rows = 2; gap = '16px';
                } else if (participantCount <= 9) {
                    cols = 3; rows = 3; gap = '12px';
                } else if (participantCount <= 12) {
                    cols = 4; rows = 3; gap = '10px';
                } else if (participantCount <= 16) {
                    cols = 4; rows = 4; gap = '8px';
                } else if (participantCount <= 20) {
                    cols = 5; rows = 4; gap = '8px';
                } else {
                    cols = 5; rows = 5; gap = '8px';
                }

                videosGrid.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
                videosGrid.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
                videosGrid.style.gap = gap;
                videosGrid.style.overflowX = 'hidden';
                videosGrid.style.overflowY = 'hidden';
            }
        }

        // Add video element for remote user
        function addVideoElement(userId, stream) {
            // Remove existing video if any
            removeVideoElement(userId);

            const videoContainer = document.createElement('div');
            videoContainer.className = 'video-container';
            videoContainer.id = `video-${userId}`;

            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;

            const label = document.createElement('div');
            label.className = 'video-label';

            // Find participant name
            const participant = participants.find(p => p.id === userId);
            label.textContent = participant ? participant.name : 'Remote User';

            videoContainer.appendChild(video);
            videoContainer.appendChild(label);
            document.getElementById('videosGrid').appendChild(videoContainer);

            // Update grid layout after adding video
            updateGridLayout();
        }

        // Remove video element
        function removeVideoElement(userId) {
            const videoElement = document.getElementById(`video-${userId}`);
            if (videoElement) {
                videoElement.remove();
                // Update grid layout after removing video
                updateGridLayout();
            }
        }

        // Control functions
        function toggleVideo() {
            isVideoEnabled = !isVideoEnabled;
            const videoTrack = localStream.getVideoTracks()[0];
            if (videoTrack) {
                videoTrack.enabled = isVideoEnabled;
            }
            updateVideoButton();
        }

        function toggleAudio() {
            isAudioEnabled = !isAudioEnabled;
            const audioTrack = localStream.getAudioTracks()[0];
            if (audioTrack) {
                audioTrack.enabled = isAudioEnabled;
            }
            updateAudioButton();
        }

        function updateVideoButton() {
            const btn = document.getElementById('videoBtn');
            btn.textContent = isVideoEnabled ? '📹' : '🚫';
            btn.className = isVideoEnabled ? 'control-btn active' : 'control-btn';
        }

        function updateAudioButton() {
            const btn = document.getElementById('audioBtn');
            btn.textContent = isAudioEnabled ? '🎤' : '🔇';
            btn.className = isAudioEnabled ? 'control-btn active' : 'control-btn';
        }

        async function toggleScreenShare() {
            if (!isScreenSharing) {
                try {
                    const screenStream = await navigator.mediaDevices.getDisplayMedia({
                        video: true,
                        audio: true
                    });

                    // Replace video track in all peer connections
                    const videoTrack = screenStream.getVideoTracks()[0];
                    Object.values(peerConnections).forEach(pc => {
                        const sender = pc.getSenders().find(s =>
                            s.track && s.track.kind === 'video'
                        );
                        if (sender) {
                            sender.replaceTrack(videoTrack);
                        }
                    });

                    // Update local video
                    document.getElementById('localVideo').srcObject = screenStream;

                    isScreenSharing = true;
                    document.getElementById('screenBtn').className = 'control-btn active';

                    // Handle screen share end
                    videoTrack.onended = () => {
                        stopScreenShare();
                    };

                } catch (error) {
                    console.error('Error sharing screen:', error);
                }
            } else {
                stopScreenShare();
            }
        }

        async function stopScreenShare() {
            try {
                // Get camera stream back
                const cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });

                // Replace video track in all peer connections
                const videoTrack = cameraStream.getVideoTracks()[0];
                Object.values(peerConnections).forEach(pc => {
                    const sender = pc.getSenders().find(s =>
                        s.track && s.track.kind === 'video'
                    );
                    if (sender) {
                        sender.replaceTrack(videoTrack);
                    }
                });

                // Update local video
                document.getElementById('localVideo').srcObject = cameraStream;
                localStream = cameraStream;

                isScreenSharing = false;
                document.getElementById('screenBtn').className = 'control-btn';

            } catch (error) {
                console.error('Error stopping screen share:', error);
            }
        }

        function toggleChat() {
            const chatPanel = document.getElementById('chatPanel');
            const participantsPanel = document.getElementById('participantsPanel');

            if (chatPanel.style.display === 'none' || chatPanel.style.display === '') {
                chatPanel.style.display = 'flex';
                participantsPanel.style.display = 'none';
            } else {
                chatPanel.style.display = 'none';
            }
        }

        function toggleParticipants() {
            const participantsPanel = document.getElementById('participantsPanel');
            const chatPanel = document.getElementById('chatPanel');

            if (participantsPanel.style.display === 'none' || participantsPanel.style.display === '') {
                participantsPanel.style.display = 'flex';
                chatPanel.style.display = 'none';
                updateParticipantsList();
            } else {
                participantsPanel.style.display = 'none';
            }
        }

        // Chat functions
        function handleChatKeypress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            // Validate message
            if (!message) {
                return;
            }

            if (message.length > 500) {
                alert('Message is too long. Please keep it under 500 characters.');
                return;
            }

            // Check for spam (prevent sending same message repeatedly)
            if (lastMessage === message && Date.now() - lastMessageTime < 2000) {
                alert('Please wait before sending the same message again.');
                return;
            }

            // Check for excessive caps (more than 70% uppercase)
            const uppercaseCount = (message.match(/[A-Z]/g) || []).length;
            if (message.length > 10 && uppercaseCount / message.length > 0.7) {
                alert('Please avoid excessive use of capital letters.');
                return;
            }

            if (currentPrivateChat) {
                // Send private message
                socket.emit('private-message', {
                    message: message,
                    targetId: currentPrivateChat.id
                });
            } else {
                // Send public message
                socket.emit('chat-message', { message });
            }

            input.value = '';

            // Track last message for spam prevention
            lastMessage = message;
            lastMessageTime = Date.now();
        }

        function addChatMessage(sender, message, timestamp) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageElement = document.createElement('div');
            messageElement.className = 'message';

            messageElement.innerHTML = `
                <div class="message-sender">${sender}</div>
                <div class="message-time">${timestamp}</div>
                <div>${message}</div>
            `;

            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function addPrivateChatMessage(sender, message, timestamp, isSent, senderId, targetId) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageElement = document.createElement('div');
            messageElement.className = isSent ? 'message private-sent' : 'message private';

            const targetName = isSent ?
                participants.find(p => p.id === targetId)?.name || 'Unknown' :
                sender;

            messageElement.innerHTML = `
                <div class="private-indicator">🔒 Private ${isSent ? 'to' : 'from'} ${isSent ? targetName : sender}</div>
                <div class="message-sender">${sender}</div>
                <div class="message-time">${timestamp}</div>
                <div>${message}</div>
            `;

            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Show chat panel if private message received
            if (!isSent) {
                const chatPanel = document.getElementById('chatPanel');
                if (chatPanel.style.display === 'none' || chatPanel.style.display === '') {
                    toggleChat();
                }
            }
        }

        function updateParticipantsList() {
            const participantsList = document.getElementById('participantsList');
            participantsList.innerHTML = '';

            // Add current user first
            const currentUser = participants.find(p => p.id === socket.id);
            const isCurrentUserHost = currentUser && currentUser.isHost;

            const currentUserItem = document.createElement('div');
            currentUserItem.className = 'participant-item';
            currentUserItem.innerHTML = `
                <div class="participant-info">
                    <div class="participant-avatar">${userName.charAt(0).toUpperCase()}</div>
                    <div>
                        <div class="participant-name">${userName} (You)</div>
                        <div class="participant-status">${isCurrentUserHost ? 'Host' : 'Participant'}</div>
                    </div>
                </div>
            `;
            participantsList.appendChild(currentUserItem);

            // Add other participants
            participants.forEach(participant => {
                if (participant.id !== socket.id) {
                    const participantItem = document.createElement('div');
                    participantItem.className = 'participant-item';
                    participantItem.innerHTML = `
                        <div class="participant-info">
                            <div class="participant-avatar">${participant.name.charAt(0).toUpperCase()}</div>
                            <div>
                                <div class="participant-name">${participant.name}</div>
                                <div class="participant-status">${participant.isHost ? 'Host' : 'Participant'}</div>
                            </div>
                        </div>
                        <button class="chat-btn" onclick="startPrivateChat('${participant.id}', '${participant.name}')">Chat</button>
                    `;
                    participantsList.appendChild(participantItem);
                }
            });
        }

        function startPrivateChat(participantId, participantName) {
            currentPrivateChat = { id: participantId, name: participantName };

            // Switch to chat panel
            toggleChat();

            // Update chat input placeholder
            const messageInput = document.getElementById('messageInput');
            messageInput.placeholder = `Private message to ${participantName}...`;
            messageInput.focus();

            // Add a system message to indicate private chat mode
            const messagesContainer = document.getElementById('chatMessages');
            const systemMessage = document.createElement('div');
            systemMessage.className = 'message';
            systemMessage.style.background = '#374151';
            systemMessage.style.fontStyle = 'italic';
            systemMessage.innerHTML = `
                <div style="color: #fbbf24; font-size: 0.875rem;">
                    🔒 Private chat with ${participantName} started.
                    <button onclick="exitPrivateChat()" style="background: none; border: none; color: #3b82f6; cursor: pointer; text-decoration: underline;">Exit private chat</button>
                </div>
            `;
            messagesContainer.appendChild(systemMessage);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function exitPrivateChat() {
            currentPrivateChat = null;
            const messageInput = document.getElementById('messageInput');
            messageInput.placeholder = 'Type a message...';

            // Add system message
            const messagesContainer = document.getElementById('chatMessages');
            const systemMessage = document.createElement('div');
            systemMessage.className = 'message';
            systemMessage.style.background = '#374151';
            systemMessage.style.fontStyle = 'italic';
            systemMessage.innerHTML = `
                <div style="color: #fbbf24; font-size: 0.875rem;">
                    💬 Switched back to group chat
                </div>
            `;
            messagesContainer.appendChild(systemMessage);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Utility functions
        function updateParticipantCount(count) {
            const participantCount = count || Object.keys(peerConnections).length + 1;
            document.getElementById('participantCount').textContent = participantCount;
        }

        function copyRoomId() {
            navigator.clipboard.writeText(roomId);
            alert('Room ID copied to clipboard!');
        }

        function copyRoomLink() {
            const link = window.location.href;
            navigator.clipboard.writeText(link);
            alert('Room link copied to clipboard! Share it with friends.');
        }

        function leaveRoom() {
            if (confirm('Are you sure you want to leave the room?')) {
                window.location.href = '/';
            }
        }

        // Initialize the app when page loads
        window.addEventListener('load', init);

        // Handle page unload
        window.addEventListener('beforeunload', () => {
            Object.values(peerConnections).forEach(pc => pc.close());
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>
</html>
